<?php
require_once 'app/config/config.php';

echo "<h2>Checking Existing Employee Tables</h2>";

try {
    // Check if employees table exists
    echo "<h3>1. Employees Table:</h3>";
    try {
        $stmt = $conn->query("SELECT * FROM employees LIMIT 5");
        $employees = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($employees)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background-color: #f15b31; color: white;'>";
            foreach ($employees[0] as $key => $value) {
                echo "<th>$key</th>";
            }
            echo "</tr>";
            
            foreach ($employees as $employee) {
                echo "<tr>";
                foreach ($employee as $value) {
                    echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
            echo "<p><strong>Total employees: " . count($employees) . "</strong></p>";
        } else {
            echo "<p>Employees table is empty.</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>Employees table does not exist: " . $e->getMessage() . "</p>";
    }
    
    // Check users table
    echo "<h3>2. Users Table:</h3>";
    try {
        $stmt = $conn->query("SELECT id, username, email, first_name, last_name, role, department FROM users LIMIT 5");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($users)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background-color: #f15b31; color: white;'>";
            foreach ($users[0] as $key => $value) {
                echo "<th>$key</th>";
            }
            echo "</tr>";
            
            foreach ($users as $user) {
                echo "<tr>";
                foreach ($user as $value) {
                    echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
            echo "<p><strong>Total users: " . count($users) . "</strong></p>";
        } else {
            echo "<p>Users table is empty.</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>Users table error: " . $e->getMessage() . "</p>";
    }
    
    // Check departments table
    echo "<h3>3. Departments Table:</h3>";
    try {
        $stmt = $conn->query("SELECT * FROM departments LIMIT 5");
        $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($departments)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background-color: #f15b31; color: white;'>";
            foreach ($departments[0] as $key => $value) {
                echo "<th>$key</th>";
            }
            echo "</tr>";
            
            foreach ($departments as $dept) {
                echo "<tr>";
                foreach ($dept as $value) {
                    echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
            echo "<p><strong>Total departments: " . count($departments) . "</strong></p>";
        } else {
            echo "<p>Departments table is empty.</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>Departments table does not exist: " . $e->getMessage() . "</p>";
    }
    
    // Show current payroll data
    echo "<h3>4. Current Payroll Data:</h3>";
    $stmt = $conn->query("SELECT user_id, COUNT(*) as count FROM payroll GROUP BY user_id");
    $payroll_summary = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f15b31; color: white;'>";
    echo "<th>User ID</th><th>Payroll Records</th></tr>";
    foreach ($payroll_summary as $summary) {
        echo "<tr>";
        echo "<td>" . $summary['user_id'] . "</td>";
        echo "<td>" . $summary['count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";

} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>
