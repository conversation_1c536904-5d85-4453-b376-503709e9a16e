<?php
require_once 'app/config/config.php';

echo "<h2>Users Table Structure</h2>";
try {
    $stmt = $conn->query("DESCRIBE users");
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f15b31; color: white;'>";
    echo "<th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";

    echo "<h2>Sample User Data</h2>";
    $stmt = $conn->query("SELECT * FROM users WHERE id = 16 LIMIT 1");
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f15b31; color: white;'>";
        foreach ($user as $key => $value) {
            echo "<th>$key</th>";
        }
        echo "</tr><tr>";
        foreach ($user as $key => $value) {
            echo "<td>$value</td>";
        }
        echo "</tr></table>";
    } else {
        echo "<p>No user found with ID = 16</p>";
    }

} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
