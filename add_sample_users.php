<?php
require_once 'app/config/config.php';

echo "<h2>Adding Sample Users for Payroll</h2>";

try {
    // Check if users with IDs 16, 11, 10 already exist
    $existing_users = [];
    foreach ([16, 11, 10] as $user_id) {
        $stmt = $conn->prepare("SELECT id FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        if ($stmt->fetch()) {
            $existing_users[] = $user_id;
        }
    }
    
    if (!empty($existing_users)) {
        echo "<p style='color: orange;'>Users with IDs " . implode(', ', $existing_users) . " already exist. Skipping...</p>";
    }
    
    // Insert sample users for payroll
    $sample_users = [
        [
            'id' => 16,
            'username' => 'juan.dela.cruz',
            'email' => '<EMAIL>',
            'password' => password_hash('password123', PASSWORD_DEFAULT),
            'first_name' => 'Juan',
            'last_name' => '<PERSON><PERSON>',
            'role' => 'user',
            'department' => 'Finance'
        ],
        [
            'id' => 11,
            'username' => 'maria.santos',
            'email' => '<EMAIL>',
            'password' => password_hash('password123', PASSWORD_DEFAULT),
            'first_name' => 'Maria',
            'last_name' => 'Santos',
            'role' => 'user',
            'department' => 'HR'
        ],
        [
            'id' => 10,
            'username' => 'pedro.garcia',
            'email' => '<EMAIL>',
            'password' => password_hash('password123', PASSWORD_DEFAULT),
            'first_name' => 'Pedro',
            'last_name' => 'Garcia',
            'role' => 'user',
            'department' => 'Operations'
        ]
    ];
    
    $inserted = 0;
    foreach ($sample_users as $user) {
        if (!in_array($user['id'], $existing_users)) {
            try {
                $stmt = $conn->prepare("
                    INSERT INTO users (id, username, email, password, first_name, last_name, role, department) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $user['id'],
                    $user['username'],
                    $user['email'],
                    $user['password'],
                    $user['first_name'],
                    $user['last_name'],
                    $user['role'],
                    $user['department']
                ]);
                echo "<p style='color: green;'>✅ Added user: {$user['first_name']} {$user['last_name']} (ID: {$user['id']})</p>";
                $inserted++;
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ Error adding user {$user['first_name']} {$user['last_name']}: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h3>Summary:</h3>";
    echo "<p>✅ Inserted $inserted new users</p>";
    echo "<p>📋 Total users for payroll: " . (count($sample_users) - count($existing_users) + $inserted) . "</p>";
    
    echo "<h3>Now check your payroll page:</h3>";
    echo "<a href='app/modules/user/admin_payroll.php' style='background-color: #f15b31; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Payroll with Names</a>";

} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>
