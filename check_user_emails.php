<?php
require_once 'app/config/config.php';

echo "<h2>Check User Emails for Payroll</h2>";

try {
    // Check users with IDs 16 and 11
    $user_ids = [16, 11, 10];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f15b31; color: white;'>";
    echo "<th>User ID</th><th>Username</th><th>Email</th><th>Department</th><th>Role</th><th>First Name</th><th>Last Name</th></tr>";
    
    foreach ($user_ids as $user_id) {
        $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<tr>";
        echo "<td>$user_id</td>";
        if ($user) {
            echo "<td>" . ($user['username'] ?? 'NULL') . "</td>";
            echo "<td>" . ($user['email'] ?? 'NULL') . "</td>";
            echo "<td>" . ($user['department'] ?? 'NULL') . "</td>";
            echo "<td>" . ($user['role'] ?? 'NULL') . "</td>";
            echo "<td>" . ($user['first_name'] ?? 'NULL') . "</td>";
            echo "<td>" . ($user['last_name'] ?? 'NULL') . "</td>";
        } else {
            echo "<td colspan='6'>USER NOT FOUND</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>All Users in Database:</h3>";
    $stmt = $conn->query("SELECT id, username, email, department, role FROM users ORDER BY id");
    $all_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f15b31; color: white;'>";
    echo "<th>ID</th><th>Username</th><th>Email</th><th>Department</th><th>Role</th></tr>";
    
    foreach ($all_users as $user) {
        echo "<tr>";
        echo "<td>" . $user['id'] . "</td>";
        echo "<td>" . ($user['username'] ?? 'NULL') . "</td>";
        echo "<td>" . ($user['email'] ?? 'NULL') . "</td>";
        echo "<td>" . ($user['department'] ?? 'NULL') . "</td>";
        echo "<td>" . ($user['role'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";

} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
