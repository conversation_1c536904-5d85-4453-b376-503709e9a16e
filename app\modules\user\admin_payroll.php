<?php
session_start();
require_once '../../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header("Location: ../auth/login.php");
    exit();
}

// Create payroll table if it doesn't exist
try {
    $conn->exec("CREATE TABLE IF NOT EXISTS payroll (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        pay_rate DECIMAL(10,2) NOT NULL,
        hours_worked DECIMAL(10,2) NOT NULL,
        gross_pay DECIMAL(10,2) NOT NULL,
        deductions DECIMAL(10,2) NOT NULL,
        net_pay DECIMAL(10,2) NOT NULL,
        pay_period VARCHAR(50) NOT NULL,
        payment_date DATE NOT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
} catch (PDOException $e) {
    // Continue if error
}

// Get payroll records - IMPROVED VERSION to match finance payroll exactly
try {
    // Use the same query structure as finance/payroll.php for consistency
    $payroll_sql = "SELECT p.*, CONCAT(u.first_name, ' ', u.last_name) as employee_name, u.email, u.department, u.role
                   FROM payroll p
                   JOIN users u ON p.user_id = u.id
                   ORDER BY p.payment_date DESC, employee_name";
    $stmt = $conn->prepare($payroll_sql);
    $stmt->execute();
    $payroll_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Add fallback for any records that might not have proper user data
    foreach ($payroll_records as &$record) {
        // Ensure employee_name is not empty
        if (empty($record['employee_name']) || trim($record['employee_name']) == '') {
            // Try to get data from employees table as fallback
            try {
                $emp_stmt = $conn->prepare("SELECT name, department, pay_per_hour FROM employees WHERE id = ?");
                $emp_stmt->execute([$record['user_id']]);
                $employee = $emp_stmt->fetch(PDO::FETCH_ASSOC);

                if ($employee) {
                    $record['employee_name'] = $employee['name'];
                    $record['department'] = $employee['department'] ?: $record['department'] ?: 'N/A';
                } else {
                    // Final fallback - use email or user_id
                    $record['employee_name'] = $record['email'] ?: "User ID: " . $record['user_id'];
                }
            } catch (Exception $e) {
                $record['employee_name'] = "User ID: " . $record['user_id'];
            }
        }

        // Ensure other fields have default values
        $record['department'] = $record['department'] ?: 'N/A';
        $record['role'] = $record['role'] ?: 'Employee';
    }
} catch (PDOException $e) {
    $payroll_records = [];
    // If the JOIN fails, try the old method as fallback
    try {
        $stmt = $conn->prepare("SELECT * FROM payroll ORDER BY payment_date DESC");
        $stmt->execute();
        $payroll_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Add employee data for each record
        foreach ($payroll_records as &$record) {
            try {
                // Try users table first
                $user_stmt = $conn->prepare("SELECT first_name, last_name, email, username, department, role FROM users WHERE id = ?");
                $user_stmt->execute([$record['user_id']]);
                $user = $user_stmt->fetch(PDO::FETCH_ASSOC);

                if ($user) {
                    if (!empty($user['first_name']) || !empty($user['last_name'])) {
                        $record['employee_name'] = trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? ''));
                    } else {
                        $record['employee_name'] = $user['email'] ?: $record['user_id'];
                    }
                    $record['department'] = $user['department'] ?: 'N/A';
                    $record['role'] = $user['role'] ?: 'Employee';
                } else {
                    $record['employee_name'] = "User ID: " . $record['user_id'];
                    $record['department'] = 'N/A';
                    $record['role'] = 'Employee';
                }
            } catch (Exception $e) {
                $record['employee_name'] = "User ID: " . $record['user_id'];
                $record['department'] = 'N/A';
                $record['role'] = 'Employee';
            }
        }
    } catch (PDOException $e2) {
        $payroll_records = [];
    }
}

// Calculate payroll statistics
$total_gross_pay = 0;
$total_net_pay = 0;
$total_deductions = 0;
$processed_count = 0;
$pending_count = 0;

foreach ($payroll_records as $record) {
    $total_gross_pay += $record['gross_pay'];
    $total_net_pay += $record['net_pay'];
    $total_deductions += $record['deductions'];

    if ($record['status'] == 'processed') {
        $processed_count++;
    } else {
        $pending_count++;
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Payroll Overview</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #faf2e9;
        }

        .page-title {
            background-color: #f15b31;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }

        .card-header {
            background-color: #f15b31 !important;
            color: white !important;
        }

        .btn-primary {
            background-color: #f15b31;
            border-color: #f15b31;
        }

        .btn-primary:hover {
            background-color: #d14118;
            border-color: #d14118;
        }

        .table th {
            background-color: #f15b31;
            color: white;
        }

        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #f15b31;
        }

        .stats-card h6 {
            color: #f15b31;
            margin-bottom: 10px;
        }

        .stats-card h3 {
            color: #333;
            margin: 0;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <h1 class="page-title">💰 Admin - Payroll Overview</h1>

        <!-- Navigation Buttons -->
        <div class="row mb-4">
            <div class="col-6">
                <a href="../finance/payroll.php" class="btn" style="background-color: #28a745; color: white;">
                    <i class="fas fa-plus"></i> Add New Payroll
                </a>
            </div>
            <div class="col-6 text-end">
                <a href="hr_reports.php" class="btn" style="background-color: #f15b31; color: white;">Back to HR Reports</a>
            </div>
        </div>

        <!-- Payroll Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <h6>Total Gross Pay</h6>
                    <h3>₱<?php echo number_format($total_gross_pay, 2); ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h6>Total Net Pay</h6>
                    <h3>₱<?php echo number_format($total_net_pay, 2); ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h6>Total Deductions</h6>
                    <h3>₱<?php echo number_format($total_deductions, 2); ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h6>Processed/Pending</h6>
                    <h3><?php echo $processed_count; ?>/<?php echo $pending_count; ?></h3>
                </div>
            </div>
        </div>

        <!-- Payroll Records -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">All Payroll Records</h4>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn btn-sm btn-light">
                    <i class="fas fa-sync-alt"></i> Refresh
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Employee</th>
                                <th>Department</th>
                                <th>Role</th>
                                <th>Pay Rate</th>
                                <th>Hours</th>
                                <th>Gross Pay</th>
                                <th>Deductions</th>
                                <th>Net Pay</th>
                                <th>Pay Period</th>
                                <th>Payment Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (count($payroll_records) > 0): ?>
                                <?php foreach ($payroll_records as $record): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($record['id']); ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($record['employee_name']); ?></strong>
                                        </td>
                                        <td><?php echo htmlspecialchars($record['department'] ?? 'N/A'); ?></td>
                                        <td><?php echo htmlspecialchars($record['role'] ?? 'N/A'); ?></td>
                                        <td>₱<?php echo number_format($record['pay_rate'], 2); ?></td>
                                        <td><?php echo number_format($record['hours_worked'], 1); ?> hrs</td>
                                        <td>₱<?php echo number_format($record['gross_pay'], 2); ?></td>
                                        <td>₱<?php echo number_format($record['deductions'], 2); ?></td>
                                        <td><strong>₱<?php echo number_format($record['net_pay'], 2); ?></strong></td>
                                        <td><?php echo htmlspecialchars($record['pay_period']); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($record['payment_date'])); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $record['status'] == 'processed' ? 'success' : 'warning'; ?>">
                                                <?php echo ucfirst($record['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="12" class="text-center">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle"></i> <strong>No payroll records found.</strong>
                                            <br><br>
                                            <p>To add payroll data that will appear here:</p>
                                            <ol class="text-start" style="display: inline-block;">
                                                <li>Click the <strong>"Add New Payroll"</strong> button above</li>
                                                <li>Or go to <a href="../finance/payroll.php" class="btn btn-sm btn-primary">Finance → Payroll Management</a></li>
                                                <li>Add payroll entries for employees</li>
                                                <li>Return here to view all payroll data in the admin overview</li>
                                            </ol>
                                            <br>
                                            <small class="text-muted">
                                                All payroll data added in the Finance module will automatically appear in this Admin overview.
                                                <br>Database Status: <?php echo isset($conn) ? 'Connected' : 'Not connected'; ?> |
                                                Records Found: <?php echo count($payroll_records); ?>
                                            </small>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Back to Dashboard Button -->
        <div class="text-center mt-4 mb-4">
            <a href="../admin/admin_dashboard.php" class="btn btn-primary">Back to Dashboard</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>